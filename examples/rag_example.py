"""Example usage of the RAG component."""

import os
from ai_building_blocks.rag import <PERSON><PERSON><PERSON>, Document, VectorStoreConfig

def main():
    """Demonstrate RAG functionality."""
    
    # Check if API key is available
    if not os.getenv("OPENAI_API_KEY"):
        print("Please set OPENAI_API_KEY environment variable")
        print("You can copy .env.example to .env and add your API key")
        return
    
    print("Creating RAG client...")
    
    # Create custom vector store configuration
    config = VectorStoreConfig(
        chunk_size=500,  # Smaller chunks for this example
        chunk_overlap=50,
        top_k=3,  # Retrieve top 3 most relevant chunks
        score_threshold=0.1
    )
    
    # Create RAG client
    rag = RAGClient(
        model="gpt-3.5-turbo",
        temperature=0.1,  # Low temperature for factual responses
        vector_store_config=config
    )
    
    print("RAG client created successfully!")
    print("\n" + "="*60)
    
    # Example 1: Add documents to knowledge base
    print("\n1. Adding documents to knowledge base:")
    
    documents = [
        Document(
            content="""
            Python is a high-level, interpreted programming language with dynamic semantics. 
            Its high-level built-in data structures, combined with dynamic typing and dynamic binding, 
            make it very attractive for Rapid Application Development, as well as for use as a 
            scripting or glue language to connect existing components together.
            """,
            source="python_intro.txt",
            metadata={"topic": "programming", "language": "python"}
        ),
        Document(
            content="""
            Machine Learning is a subset of artificial intelligence (AI) that provides systems 
            the ability to automatically learn and improve from experience without being explicitly 
            programmed. Machine learning focuses on the development of computer programs that can 
            access data and use it to learn for themselves.
            """,
            source="ml_intro.txt",
            metadata={"topic": "ai", "subtopic": "machine_learning"}
        ),
        Document(
            content="""
            LangChain is a framework for developing applications powered by language models. 
            It enables applications that are context-aware and can reason about their environment. 
            LangChain provides tools for prompt management, chains, data augmented generation, 
            agents, and memory.
            """,
            source="langchain_intro.txt",
            metadata={"topic": "ai", "subtopic": "frameworks"}
        ),
        Document(
            content="""
            Vector databases are specialized databases designed to store and query high-dimensional 
            vectors efficiently. They are essential for applications like semantic search, 
            recommendation systems, and retrieval-augmented generation (RAG). Popular vector 
            databases include Pinecone, Weaviate, and Chroma.
            """,
            source="vector_db_intro.txt",
            metadata={"topic": "databases", "subtopic": "vector_databases"}
        )
    ]
    
    doc_ids = rag.add_documents(documents)
    print(f"Added {len(documents)} documents with IDs: {doc_ids}")
    
    # Show knowledge base statistics
    stats = rag.get_stats()
    print(f"Knowledge base stats: {stats}")
    
    # Example 2: Query the RAG system
    print("\n2. Querying the RAG system:")
    
    questions = [
        "What is Python?",
        "Tell me about machine learning",
        "What is LangChain used for?",
        "How do vector databases work?",
        "What's the difference between Python and machine learning?"
    ]
    
    for i, question in enumerate(questions, 1):
        print(f"\nQuestion {i}: {question}")
        print("-" * 50)
        
        response = rag.query(question)
        
        print(f"Answer: {response.answer}")
        print(f"Sources used: {response.sources}")
        print(f"Retrieved {len(response.retrieved_documents)} relevant chunks")
        
        # Show retrieved document details
        for j, doc in enumerate(response.retrieved_documents, 1):
            print(f"  {j}. Score: {doc.score:.3f}, Source: {doc.document.source}")
    
    # Example 3: Search documents without generating answer
    print("\n3. Searching documents (retrieval only):")
    
    search_query = "artificial intelligence"
    retrieved_docs = rag.search_documents(search_query, top_k=2)
    
    print(f"Search query: '{search_query}'")
    print(f"Found {len(retrieved_docs)} relevant documents:")
    
    for i, doc in enumerate(retrieved_docs, 1):
        print(f"\n{i}. Score: {doc.score:.3f}")
        print(f"   Source: {doc.document.source}")
        print(f"   Content preview: {doc.content[:100]}...")
    
    # Example 4: Add more documents using simple text
    print("\n4. Adding simple texts:")
    
    texts = [
        "FastAPI is a modern, fast web framework for building APIs with Python 3.7+",
        "Docker is a platform for developing, shipping, and running applications in containers",
        "Kubernetes is an open-source container orchestration platform"
    ]
    
    sources = ["fastapi.txt", "docker.txt", "kubernetes.txt"]
    metadatas = [
        {"topic": "web_frameworks"},
        {"topic": "containerization"},
        {"topic": "orchestration"}
    ]
    
    text_ids = rag.add_texts(texts, sources=sources, metadatas=metadatas)
    print(f"Added {len(texts)} texts with IDs: {text_ids}")
    
    # Query about the new content
    response = rag.query("What is FastAPI?")
    print(f"\nQuery: What is FastAPI?")
    print(f"Answer: {response.answer}")
    
    # Example 5: Final knowledge base statistics
    print("\n5. Final knowledge base statistics:")
    final_stats = rag.get_stats()
    print(f"Final stats: {final_stats}")
    
    print("\n" + "="*60)
    print("RAG example completed!")


if __name__ == "__main__":
    main()
