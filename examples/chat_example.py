"""Example usage of the chat component."""

import os
from ai_building_blocks.chat import ChatClient

def main():
    """Demonstrate basic chat functionality."""
    
    # Check if API key is available
    if not os.getenv("OPENAI_API_KEY"):
        print("Please set OPENAI_API_KEY environment variable")
        print("You can copy .env.example to .env and add your API key")
        return
    
    # Create a chat client
    print("Creating chat client...")
    chat = ChatClient(
        model="gpt-3.5-turbo",  # Using cheaper model for example
        temperature=0.7,
        system_message="You are a helpful assistant that provides concise and friendly responses."
    )
    
    print("Chat client created successfully!")
    print("System message set: You are a helpful assistant...")
    print("\n" + "="*50)
    
    # Example 1: Simple conversation
    print("\n1. Simple conversation:")
    response = chat.send_message("Hello! What can you help me with?")
    print(f"User: Hello! What can you help me with?")
    print(f"Assistant: {response.content}")
    
    # Example 2: Follow-up question (tests memory)
    print("\n2. Follow-up question (testing memory):")
    response = chat.send_message("What did I just ask you?")
    print(f"User: What did I just ask you?")
    print(f"Assistant: {response.content}")
    
    # Example 3: Multiple messages
    print("\n3. Sending multiple messages:")
    messages = [
        "What is Python?",
        "What are its main advantages?",
        "Can you give me a simple example?"
    ]
    
    responses = chat.send_messages(messages)
    for i, (msg, resp) in enumerate(zip(messages, responses), 1):
        print(f"\nMessage {i}:")
        print(f"User: {msg}")
        print(f"Assistant: {resp.content}")
    
    # Example 4: Show conversation history
    print("\n4. Conversation history:")
    history = chat.get_history()
    print(f"Total messages in history: {len(history.messages)}")
    
    for i, msg in enumerate(history.messages[-3:], 1):  # Show last 3 messages
        print(f"{i}. [{msg.role}]: {msg.content[:100]}...")
    
    # Example 5: Clear history and start fresh
    print("\n5. Clearing history and starting fresh:")
    chat.clear_history(keep_system=True)
    print("History cleared (keeping system message)")
    
    response = chat.send_message("Do you remember our previous conversation?")
    print(f"User: Do you remember our previous conversation?")
    print(f"Assistant: {response.content}")
    
    print("\n" + "="*50)
    print("Chat example completed!")


if __name__ == "__main__":
    main()
