"""Example usage of the agent component."""

import os
from ai_building_blocks.agent import AgentClient

def main():
    """Demonstrate React Agent functionality."""
    
    # Check if API key is available
    if not os.getenv("OPENAI_API_KEY"):
        print("Please set OPENAI_API_KEY environment variable")
        print("You can copy .env.example to .env and add your API key")
        return
    
    print("Creating React Agent client...")
    
    # Create agent client
    agent = AgentClient(
        model="gpt-3.5-turbo",  # Using cheaper model for example
        temperature=0.1,  # Low temperature for more consistent reasoning
        max_iterations=8,  # Allow up to 8 reasoning steps
        verbose=True  # Enable verbose logging to see reasoning process
    )
    
    print("Agent client created successfully!")
    print(f"Available tools: {', '.join(agent.get_available_tools())}")
    print("\n" + "="*60)
    
    # Example 1: Simple calculation
    print("\n1. Mathematical calculation:")
    print("Task: Calculate the area of a circle with radius 5")
    
    response = agent.run("What is the area of a circle with radius 5? Use the calculator tool.")
    
    print(f"Answer: {response.answer}")
    print(f"Tools used: {response.tools_used}")
    print(f"Reasoning steps: {response.total_iterations}")
    print(f"Execution time: {response.execution_time:.2f}s")
    
    # Show reasoning chain
    print("\nReasoning chain:")
    for i, thought in enumerate(response.reasoning_chain, 1):
        print(f"  {i}. [{thought.step.upper()}] {thought.content[:100]}...")
        if thought.tool_calls:
            for tool_call in thought.tool_calls:
                print(f"      -> Called {tool_call.tool_name} with {tool_call.arguments}")
    
    # Example 2: Multi-step problem solving
    print("\n" + "="*60)
    print("\n2. Multi-step problem solving:")
    print("Task: Calculate compound interest")
    
    response = agent.run(
        "I want to invest $1000 at 5% annual interest, compounded annually. "
        "How much will I have after 3 years? Please show the calculation step by step."
    )
    
    print(f"Answer: {response.answer}")
    print(f"Tools used: {response.tools_used}")
    print(f"Success: {response.success}")
    
    # Example 3: Information gathering (mock web search)
    print("\n" + "="*60)
    print("\n3. Information gathering:")
    print("Task: Search for information about Python programming")
    
    response = agent.run(
        "Can you search for information about Python programming language "
        "and tell me its main features?"
    )
    
    print(f"Answer: {response.answer}")
    print(f"Tools used: {response.tools_used}")
    
    # Example 4: File operations
    print("\n" + "="*60)
    print("\n4. File operations:")
    print("Task: Create a simple text file")
    
    response = agent.run(
        "Create a text file called 'hello.txt' with the content 'Hello from AI Agent!' "
        "and then read it back to confirm it was created correctly."
    )
    
    print(f"Answer: {response.answer}")
    print(f"Tools used: {response.tools_used}")
    
    # Example 5: Complex reasoning with multiple tools
    print("\n" + "="*60)
    print("\n5. Complex multi-tool task:")
    print("Task: Calculate and save results")
    
    response = agent.run(
        "Calculate the factorial of 5 (5!) using the calculator, "
        "then search for information about factorials, "
        "and finally save both the result and a brief explanation to a file called 'factorial_info.txt'."
    )
    
    print(f"Answer: {response.answer}")
    print(f"Tools used: {response.tools_used}")
    print(f"Total reasoning steps: {response.total_iterations}")
    
    # Show detailed tool usage statistics
    print("\n" + "="*60)
    print("\n6. Tool usage summary:")
    
    all_responses = [response]  # In a real app, you'd collect all responses
    total_tool_calls = sum(r.tool_calls_count for r in all_responses)
    successful_calls = sum(r.successful_tool_calls for r in all_responses)
    
    print(f"Total tool calls: {total_tool_calls}")
    print(f"Successful calls: {successful_calls}")
    print(f"Success rate: {(successful_calls/total_tool_calls*100):.1f}%" if total_tool_calls > 0 else "N/A")
    
    # Show tool usage breakdown
    tool_usage = response.get_tool_usage_summary()
    print("\nTool usage breakdown:")
    for tool_name, count in tool_usage.items():
        print(f"  {tool_name}: {count} calls")
    
    print("\n" + "="*60)
    print("Agent example completed!")
    
    # Cleanup example files
    try:
        if os.path.exists('hello.txt'):
            os.remove('hello.txt')
        if os.path.exists('factorial_info.txt'):
            os.remove('factorial_info.txt')
        print("Cleaned up example files.")
    except:
        print("Note: Some example files may remain in the directory.")


if __name__ == "__main__":
    main()