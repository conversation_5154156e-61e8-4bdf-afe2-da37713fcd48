# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Optional: OpenAI API Base URL (for custom endpoints)
# OPENAI_API_BASE=https://api.openai.com/v1

# Optional: Default model settings
# OPENAI_MODEL=gpt-4
# OPENAI_TEMPERATURE=0.7
# OPENAI_MAX_TOKENS=1000

# Optional: Tavily Search API (for agent web search capabilities)
# TAVILY_API_KEY=your_tavily_api_key_here

# Optional: LangSmith tracing (for debugging and monitoring)
# LANGCHAIN_TRACING_V2=true
# LANGCHAIN_API_KEY=your_langsmith_api_key_here
# LANGCHAIN_PROJECT=ai-building-blocks
