[project]
name = "ai-building-blocks"
version = "0.1.0"
description = "A modular Python package for rapidly building AI product prototypes"
readme = "README.md"
authors = [
    { name = "niutao", email = "<EMAIL>" }
]
requires-python = ">=3.11"
dependencies = [
    "langchain>=0.2.0",
    "langchain-core>=0.2.0",
    "langchain-openai>=0.1.0",
    "langgraph>=0.2.0",
    "openai>=1.0.0",
    "pydantic>=2.0.0",
    "tiktoken>=0.5.0",
    "numpy>=1.24.0",
    "python-dotenv>=1.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]

[project.urls]
Homepage = "https://github.com/niutao/ai-building-blocks"
Repository = "https://github.com/niutao/ai-building-blocks"
Documentation = "https://github.com/niutao/ai-building-blocks#readme"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"
