"""Data models for the RAG component."""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, ConfigDict


class Document(BaseModel):
    """Represents a document for RAG processing."""
    
    content: str = Field(..., description="The text content of the document")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata for the document")
    doc_id: Optional[str] = Field(default=None, description="Unique identifier for the document")
    source: Optional[str] = Field(default=None, description="Source of the document (file path, URL, etc.)")
    created_at: datetime = Field(default_factory=datetime.now, description="When the document was created")
    
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    def __str__(self) -> str:
        """String representation of the document."""
        preview = self.content[:100] + "..." if len(self.content) > 100 else self.content
        return f"Document(id={self.doc_id}, source={self.source}, content='{preview}')"


class RetrievedDocument(BaseModel):
    """Represents a document retrieved from vector search."""
    
    document: Document = Field(..., description="The retrieved document")
    score: float = Field(..., description="Similarity score (0.0 to 1.0)")
    rank: int = Field(..., description="Rank in the search results (1-based)")
    
    @property
    def content(self) -> str:
        """Get the content of the retrieved document."""
        return self.document.content
    
    @property
    def metadata(self) -> Dict[str, Any]:
        """Get the metadata of the retrieved document."""
        return self.document.metadata


class RAGResponse(BaseModel):
    """Response from a RAG query."""
    
    answer: str = Field(..., description="The generated answer")
    retrieved_documents: List[RetrievedDocument] = Field(
        default_factory=list, 
        description="Documents retrieved for context"
    )
    query: str = Field(..., description="The original query")
    model: Optional[str] = Field(default=None, description="The model used for generation")
    usage: Optional[Dict[str, Any]] = Field(default=None, description="Token usage information")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional response metadata")
    
    @property
    def sources(self) -> List[str]:
        """Get unique sources from retrieved documents."""
        sources = set()
        for doc in self.retrieved_documents:
            if doc.document.source:
                sources.add(doc.document.source)
        return list(sources)
    
    @property
    def context(self) -> str:
        """Get concatenated context from retrieved documents."""
        return "\n\n".join([doc.content for doc in self.retrieved_documents])


class VectorStoreConfig(BaseModel):
    """Configuration for vector store."""
    
    embedding_model: str = Field(default="text-embedding-ada-002", description="Embedding model to use")
    chunk_size: int = Field(default=1000, description="Size of text chunks for processing")
    chunk_overlap: int = Field(default=200, description="Overlap between chunks")
    top_k: int = Field(default=5, description="Number of documents to retrieve")
    score_threshold: float = Field(default=0.0, description="Minimum similarity score threshold")
    
    model_config = ConfigDict(frozen=True)


class IndexStats(BaseModel):
    """Statistics about the document index."""
    
    total_documents: int = Field(..., description="Total number of documents indexed")
    total_chunks: int = Field(..., description="Total number of text chunks")
    embedding_model: str = Field(..., description="Embedding model used")
    index_size_mb: Optional[float] = Field(default=None, description="Size of the index in MB")
    last_updated: datetime = Field(default_factory=datetime.now, description="When the index was last updated")
    
    def __str__(self) -> str:
        """String representation of index stats."""
        return (f"IndexStats(docs={self.total_documents}, chunks={self.total_chunks}, "
                f"model={self.embedding_model}, size={self.index_size_mb}MB)")
