"""Vector store implementation for RAG."""

import os
from typing import List, Optional, Dict, Any
import uuid

from langchain_core.vectorstores import InMemoryVectorStore
from langchain_openai import OpenAIEmbeddings
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_core.documents import Document as LCDocument
from dotenv import load_dotenv

from .models import Document, RetrievedDocument, VectorStoreConfig, IndexStats

# Load environment variables
load_dotenv()


class VectorStore:
    """Vector store for document indexing and retrieval."""
    
    def __init__(
        self,
        config: Optional[VectorStoreConfig] = None,
        api_key: Optional[str] = None,
    ):
        """Initialize the vector store.
        
        Args:
            config: Vector store configuration
            api_key: OpenAI API key (default: from environment)
        """
        self.config = config or VectorStoreConfig()
        
        # Initialize embeddings
        self.embeddings = OpenAIEmbeddings(
            model=self.config.embedding_model,
            api_key=api_key or os.getenv("OPENAI_API_KEY"),
        )
        
        # Initialize text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=self.config.chunk_size,
            chunk_overlap=self.config.chunk_overlap,
            length_function=len,
        )
        
        # Initialize vector store
        self.vector_store = InMemoryVectorStore(self.embeddings)
        
        # Track indexed documents
        self._documents: Dict[str, Document] = {}
        self._total_chunks = 0
    
    def add_document(self, document: Document) -> str:
        """Add a single document to the vector store.
        
        Args:
            document: Document to add
            
        Returns:
            Document ID
        """
        return self.add_documents([document])[0]
    
    def add_documents(self, documents: List[Document]) -> List[str]:
        """Add multiple documents to the vector store.
        
        Args:
            documents: List of documents to add
            
        Returns:
            List of document IDs
        """
        doc_ids = []
        lc_documents = []
        
        for doc in documents:
            # Generate ID if not provided
            if not doc.doc_id:
                doc.doc_id = str(uuid.uuid4())
            
            doc_ids.append(doc.doc_id)
            
            # Split document into chunks
            chunks = self.text_splitter.split_text(doc.content)
            
            # Create LangChain documents for each chunk
            for i, chunk in enumerate(chunks):
                metadata = doc.metadata.copy()
                metadata.update({
                    "doc_id": doc.doc_id,
                    "source": doc.source,
                    "chunk_index": i,
                    "total_chunks": len(chunks),
                    "created_at": doc.created_at.isoformat(),
                })
                
                lc_doc = LCDocument(
                    page_content=chunk,
                    metadata=metadata
                )
                lc_documents.append(lc_doc)
            
            # Store document reference
            self._documents[doc.doc_id] = doc
            self._total_chunks += len(chunks)
        
        # Add to vector store
        if lc_documents:
            self.vector_store.add_documents(lc_documents)
        
        return doc_ids
    
    def add_texts(
        self, 
        texts: List[str], 
        sources: Optional[List[str]] = None,
        metadatas: Optional[List[Dict[str, Any]]] = None
    ) -> List[str]:
        """Add texts directly to the vector store.
        
        Args:
            texts: List of text content
            sources: Optional list of sources for each text
            metadatas: Optional list of metadata for each text
            
        Returns:
            List of document IDs
        """
        documents = []
        
        for i, text in enumerate(texts):
            source = sources[i] if sources and i < len(sources) else None
            metadata = metadatas[i] if metadatas and i < len(metadatas) else {}
            
            doc = Document(
                content=text,
                source=source,
                metadata=metadata
            )
            documents.append(doc)
        
        return self.add_documents(documents)
    
    def search(
        self, 
        query: str, 
        top_k: Optional[int] = None,
        score_threshold: Optional[float] = None
    ) -> List[RetrievedDocument]:
        """Search for similar documents.
        
        Args:
            query: Search query
            top_k: Number of documents to retrieve
            score_threshold: Minimum similarity score
            
        Returns:
            List of retrieved documents with scores
        """
        k = top_k or self.config.top_k
        threshold = score_threshold or self.config.score_threshold
        
        # Perform similarity search with scores
        results = self.vector_store.similarity_search_with_score(query, k=k)
        
        retrieved_docs = []
        for rank, (lc_doc, score) in enumerate(results, 1):
            # Filter by score threshold
            if score < threshold:
                continue
            
            # Get original document
            doc_id = lc_doc.metadata.get("doc_id")
            original_doc = self._documents.get(doc_id)
            
            if original_doc:
                # Create a document with the chunk content
                chunk_doc = Document(
                    content=lc_doc.page_content,
                    metadata=lc_doc.metadata,
                    doc_id=doc_id,
                    source=original_doc.source,
                    created_at=original_doc.created_at
                )
            else:
                # Fallback: create document from chunk
                chunk_doc = Document(
                    content=lc_doc.page_content,
                    metadata=lc_doc.metadata,
                    doc_id=doc_id,
                    source=lc_doc.metadata.get("source")
                )
            
            retrieved_doc = RetrievedDocument(
                document=chunk_doc,
                score=score,
                rank=rank
            )
            retrieved_docs.append(retrieved_doc)
        
        return retrieved_docs
    
    def get_stats(self) -> IndexStats:
        """Get statistics about the indexed documents.
        
        Returns:
            Index statistics
        """
        return IndexStats(
            total_documents=len(self._documents),
            total_chunks=self._total_chunks,
            embedding_model=self.config.embedding_model,
            index_size_mb=None,  # Could be calculated if needed
        )
    
    def clear(self) -> None:
        """Clear all documents from the vector store."""
        self.vector_store = InMemoryVectorStore(self.embeddings)
        self._documents.clear()
        self._total_chunks = 0
    
    def get_document(self, doc_id: str) -> Optional[Document]:
        """Get a document by ID.
        
        Args:
            doc_id: Document ID
            
        Returns:
            Document if found, None otherwise
        """
        return self._documents.get(doc_id)
