"""RAG client implementation."""

import os
from typing import List, Optional, Dict, Any

from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_core.prompts import Chat<PERSON><PERSON>pt<PERSON>emplate
from dotenv import load_dotenv

from .models import Document, RAGResponse, VectorStoreConfig
from .vectorstore import VectorStore

# Load environment variables
load_dotenv()


class RAGClient:
    """RAG (Retrieval-Augmented Generation) client."""
    
    DEFAULT_PROMPT_TEMPLATE = """You are a helpful assistant that answers questions based on the provided context.

Context:
{context}

Question: {question}

Please provide a comprehensive answer based on the context above. If the context doesn't contain enough information to answer the question, say so clearly.

Answer:"""
    
    def __init__(
        self,
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.1,
        max_tokens: Optional[int] = None,
        api_key: Optional[str] = None,
        vector_store_config: Optional[VectorStoreConfig] = None,
        prompt_template: Optional[str] = None,
    ):
        """Initialize the RAG client.
        
        Args:
            model: The OpenAI model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens in response
            api_key: OpenAI API key (default: from environment)
            vector_store_config: Vector store configuration
            prompt_template: Custom prompt template
        """
        self.model_name = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        
        # Initialize LLM
        self.llm = ChatOpenAI(
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            api_key=api_key or os.getenv("OPENAI_API_KEY"),
        )
        
        # Initialize vector store
        self.vector_store = VectorStore(
            config=vector_store_config,
            api_key=api_key or os.getenv("OPENAI_API_KEY"),
        )
        
        # Initialize prompt template
        self.prompt_template = ChatPromptTemplate.from_template(
            prompt_template or self.DEFAULT_PROMPT_TEMPLATE
        )
    
    def add_document(self, document: Document) -> str:
        """Add a document to the knowledge base.
        
        Args:
            document: Document to add
            
        Returns:
            Document ID
        """
        return self.vector_store.add_document(document)
    
    def add_documents(self, documents: List[Document]) -> List[str]:
        """Add multiple documents to the knowledge base.
        
        Args:
            documents: List of documents to add
            
        Returns:
            List of document IDs
        """
        return self.vector_store.add_documents(documents)
    
    def add_texts(
        self, 
        texts: List[str], 
        sources: Optional[List[str]] = None,
        metadatas: Optional[List[Dict[str, Any]]] = None
    ) -> List[str]:
        """Add texts directly to the knowledge base.
        
        Args:
            texts: List of text content
            sources: Optional list of sources
            metadatas: Optional list of metadata
            
        Returns:
            List of document IDs
        """
        return self.vector_store.add_texts(texts, sources, metadatas)
    
    def query(
        self, 
        question: str,
        top_k: Optional[int] = None,
        score_threshold: Optional[float] = None,
        include_sources: bool = True,
        **llm_kwargs
    ) -> RAGResponse:
        """Query the RAG system.
        
        Args:
            question: The question to ask
            top_k: Number of documents to retrieve
            score_threshold: Minimum similarity score
            include_sources: Whether to include source information
            **llm_kwargs: Additional arguments for the LLM
            
        Returns:
            RAG response with answer and sources
        """
        # Retrieve relevant documents
        retrieved_docs = self.vector_store.search(
            query=question,
            top_k=top_k,
            score_threshold=score_threshold
        )
        
        if not retrieved_docs:
            # No relevant documents found
            answer = "I don't have enough information in my knowledge base to answer this question."
            return RAGResponse(
                answer=answer,
                retrieved_documents=[],
                query=question,
                model=self.model_name,
                metadata={"no_context": True}
            )
        
        # Prepare context from retrieved documents
        context = "\n\n".join([
            f"Source {i+1}: {doc.content}"
            for i, doc in enumerate(retrieved_docs)
        ])
        
        # Generate prompt
        messages = self.prompt_template.format_messages(
            context=context,
            question=question
        )
        
        # Get response from LLM
        response = self.llm.invoke(messages, **llm_kwargs)
        
        # Create RAG response
        return RAGResponse(
            answer=response.content,
            retrieved_documents=retrieved_docs,
            query=question,
            model=self.model_name,
            usage=getattr(response, 'usage_metadata', None),
            metadata={
                "num_retrieved": len(retrieved_docs),
                "include_sources": include_sources,
            }
        )
    
    def query_with_history(
        self,
        question: str,
        conversation_history: Optional[List[Dict[str, str]]] = None,
        **kwargs
    ) -> RAGResponse:
        """Query with conversation history for context.
        
        Args:
            question: The question to ask
            conversation_history: Previous conversation messages
            **kwargs: Additional arguments for query method
            
        Returns:
            RAG response
        """
        # For now, just use the current question
        # In a more advanced implementation, we could:
        # 1. Combine question with recent history for better retrieval
        # 2. Use conversation history to disambiguate the question
        # 3. Maintain conversation context in the prompt
        
        return self.query(question, **kwargs)
    
    def get_stats(self):
        """Get statistics about the knowledge base.
        
        Returns:
            Index statistics
        """
        return self.vector_store.get_stats()
    
    def clear_knowledge_base(self) -> None:
        """Clear all documents from the knowledge base."""
        self.vector_store.clear()
    
    def search_documents(
        self, 
        query: str, 
        top_k: Optional[int] = None,
        score_threshold: Optional[float] = None
    ):
        """Search for documents without generating an answer.
        
        Args:
            query: Search query
            top_k: Number of documents to retrieve
            score_threshold: Minimum similarity score
            
        Returns:
            List of retrieved documents
        """
        return self.vector_store.search(
            query=query,
            top_k=top_k,
            score_threshold=score_threshold
        )
