"""RAG (Retrieval-Augmented Generation) component for AI Building Blocks.

This module provides tools for building RAG applications with document indexing,
vector storage, and retrieval-enhanced generation capabilities.
"""

from .client import RAGClient
from .models import Document, RAGResponse, RetrievedDocument, VectorStoreConfig, IndexStats
from .vectorstore import VectorStore

__all__ = [
    "RAGClient",
    "Document",
    "RAGResponse",
    "RetrievedDocument",
    "VectorStore",
    "VectorStoreConfig",
    "IndexStats"
]
