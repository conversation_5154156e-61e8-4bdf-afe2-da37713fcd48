"""Data models for the chat component."""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, ConfigDict


class MessageRole(str, Enum):
    """Message role enumeration."""
    
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


class ChatMessage(BaseModel):
    """Represents a single chat message."""
    
    role: MessageRole = Field(..., description="The role of the message sender")
    content: str = Field(..., description="The content of the message")
    timestamp: datetime = Field(default_factory=datetime.now, description="When the message was created")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Additional metadata for the message")
    
    model_config = ConfigDict(use_enum_values=True)


class ChatResponse(BaseModel):
    """Response from a chat interaction."""
    
    message: ChatMessage = Field(..., description="The assistant's response message")
    usage: Optional[Dict[str, Any]] = Field(default=None, description="Token usage information")
    model: Optional[str] = Field(default=None, description="The model used for generation")
    finish_reason: Optional[str] = Field(default=None, description="Why the generation finished")
    
    @property
    def content(self) -> str:
        """Get the content of the response message."""
        return self.message.content


class ChatHistory(BaseModel):
    """Represents a conversation history."""
    
    messages: List[ChatMessage] = Field(default_factory=list, description="List of messages in the conversation")
    session_id: Optional[str] = Field(default=None, description="Unique identifier for the chat session")
    
    def add_message(self, role: MessageRole, content: str, metadata: Optional[Dict[str, Any]] = None) -> None:
        """Add a message to the history."""
        message = ChatMessage(role=role, content=content, metadata=metadata)
        self.messages.append(message)
    
    def get_messages_for_api(self) -> List[Dict[str, str]]:
        """Convert messages to format expected by OpenAI API."""
        return [{"role": msg.role, "content": msg.content} for msg in self.messages]
    
    def clear(self) -> None:
        """Clear all messages from history."""
        self.messages.clear()
    
    def get_last_n_messages(self, n: int) -> List[ChatMessage]:
        """Get the last n messages from history."""
        return self.messages[-n:] if n > 0 else []
