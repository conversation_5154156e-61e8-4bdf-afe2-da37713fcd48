"""Chat client implementation using LangChain and OpenAI."""

import os
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

from .models import ChatMessage, ChatResponse, ChatHistory, MessageRole

# Load environment variables
load_dotenv()


class ChatClient:
    """A simple chat client using LangChain and OpenAI."""
    
    def __init__(
        self,
        model: str = "gpt-4",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        api_key: Optional[str] = None,
        system_message: Optional[str] = None,
    ):
        """Initialize the chat client.
        
        Args:
            model: The OpenAI model to use (default: gpt-4)
            temperature: Sampling temperature (default: 0.7)
            max_tokens: Maximum tokens in response (default: None)
            api_key: OpenAI API key (default: from environment)
            system_message: System message to set context (default: None)
        """
        self.model_name = model
        self.temperature = temperature
        self.max_tokens = max_tokens
        
        # Initialize the Lang<PERSON>hain ChatOpenAI client
        self.llm = ChatOpenAI(
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            api_key=api_key or os.getenv("OPENAI_API_KEY"),
        )
        
        # Initialize conversation history
        self.history = ChatHistory()
        
        # Set system message if provided
        if system_message:
            self.set_system_message(system_message)
    
    def set_system_message(self, message: str) -> None:
        """Set or update the system message.
        
        Args:
            message: The system message content
        """
        # Remove existing system message if any
        self.history.messages = [msg for msg in self.history.messages if msg.role != MessageRole.SYSTEM]
        
        # Add new system message at the beginning
        system_msg = ChatMessage(role=MessageRole.SYSTEM, content=message)
        self.history.messages.insert(0, system_msg)
    
    def send_message(self, message: str, **kwargs) -> ChatResponse:
        """Send a message and get a response.
        
        Args:
            message: The user message to send
            **kwargs: Additional parameters to pass to the LLM
            
        Returns:
            ChatResponse containing the assistant's response
        """
        # Add user message to history
        self.history.add_message(MessageRole.USER, message)
        
        # Convert history to LangChain message format
        lc_messages = self._convert_to_langchain_messages(self.history.messages)
        
        # Get response from LLM
        response = self.llm.invoke(lc_messages, **kwargs)
        
        # Create response message
        response_message = ChatMessage(
            role=MessageRole.ASSISTANT,
            content=response.content,
            metadata={"model": self.model_name}
        )
        
        # Add response to history
        self.history.messages.append(response_message)
        
        # Create and return ChatResponse
        return ChatResponse(
            message=response_message,
            usage=getattr(response, 'usage_metadata', None),
            model=self.model_name,
            finish_reason=getattr(response, 'response_metadata', {}).get('finish_reason')
        )
    
    def send_messages(self, messages: List[str]) -> List[ChatResponse]:
        """Send multiple messages and get responses.
        
        Args:
            messages: List of user messages to send
            
        Returns:
            List of ChatResponse objects
        """
        responses = []
        for message in messages:
            response = self.send_message(message)
            responses.append(response)
        return responses
    
    def get_history(self) -> ChatHistory:
        """Get the current conversation history.
        
        Returns:
            ChatHistory object containing all messages
        """
        return self.history
    
    def clear_history(self, keep_system: bool = True) -> None:
        """Clear the conversation history.
        
        Args:
            keep_system: Whether to keep the system message (default: True)
        """
        if keep_system:
            system_messages = [msg for msg in self.history.messages if msg.role == MessageRole.SYSTEM]
            self.history.clear()
            self.history.messages.extend(system_messages)
        else:
            self.history.clear()
    
    def _convert_to_langchain_messages(self, messages: List[ChatMessage]) -> List[Union[SystemMessage, HumanMessage, AIMessage]]:
        """Convert ChatMessage objects to LangChain message objects.
        
        Args:
            messages: List of ChatMessage objects
            
        Returns:
            List of LangChain message objects
        """
        lc_messages = []
        for msg in messages:
            if msg.role == MessageRole.SYSTEM:
                lc_messages.append(SystemMessage(content=msg.content))
            elif msg.role == MessageRole.USER:
                lc_messages.append(HumanMessage(content=msg.content))
            elif msg.role == MessageRole.ASSISTANT:
                lc_messages.append(AIMessage(content=msg.content))
        return lc_messages
