"""AI Building Blocks - A modular Python package for rapidly building AI product prototypes.

This package provides three core components:
- chat: Simple chat interfaces using LangChain and OpenAI
- rag: Retrieval-Augmented Generation with document indexing and vector search
- agent: Intelligent agents with tool usage and state management using LangGraph

Example:
    >>> from ai_building_blocks import Chat<PERSON><PERSON>, RA<PERSON>lient, AgentClient
    >>>
    >>> # Simple chat
    >>> chat = ChatClient()
    >>> response = chat.send_message("Hello, world!")
    >>>
    >>> # RAG with documents
    >>> rag = RAGClient()
    >>> rag.add_documents(["Document content here"])
    >>> response = rag.query("What is this about?")
    >>>
    >>> # Intelligent agent
    >>> agent = AgentClient()
    >>> response = agent.run("Search for information about Python")
"""

from .chat import ChatClient, ChatMessage, ChatResponse
from .rag import RAGClient, Document, RAGResponse, VectorStore
from .agent import AgentClient, AgentState, AgentResponse, Tool, ToolRegistry

__version__ = "0.1.0"
__all__ = [
    "ChatClient",
    "ChatMessage",
    "ChatResponse",
    "RAGClient",
    "Document",
    "RAGResponse",
    "VectorStore",
    "AgentClient",
    "AgentState",
    "AgentResponse",
    "Tool",
    "ToolRegistry",
]
