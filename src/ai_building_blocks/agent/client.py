"""React Agent client implementation using LangGraph."""

import os
import time
import json
import uuid
from typing import Any, Dict, List, Optional, Union

from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from langchain_openai import ChatOpenAI
from dotenv import load_dotenv

from .models import (
    AgentState, AgentResponse, AgentStep, AgentThought, 
    ToolCall, ToolResult, AgentConfig
)
from .tools import ToolRegistry, default_tool_registry

# Load environment variables
load_dotenv()


class ReactAgent:
    """React (Reasoning and Acting) Agent implementation."""
    
    DEFAULT_SYSTEM_MESSAGE = """You are a helpful assistant that can use tools to help answer questions and complete tasks.

Available tools:
{tools_info}

You should follow the ReAct pattern:
1. THINK: Reason about the problem and plan your approach
2. ACT: Use tools when needed to gather information or perform actions  
3. OBSERVE: Analyze the results from your actions
4. Repeat steps 1-3 as needed until you can provide a final answer

When you decide to use a tool, format your response as:
TOOL_CALL: {{"tool_name": "tool_name", "arguments": {{"param": "value"}}}}

When you have enough information to answer, provide your final response starting with:
FINAL_ANSWER: [your answer here]

Be thorough in your reasoning and make sure to use tools when you need external information or capabilities."""

    def __init__(
        self,
        config: Optional[AgentConfig] = None,
        tool_registry: Optional[ToolRegistry] = None,
        api_key: Optional[str] = None
    ):
        """Initialize the React Agent.
        
        Args:
            config: Agent configuration
            tool_registry: Registry of available tools
            api_key: OpenAI API key
        """
        self.config = config or AgentConfig()
        self.tool_registry = tool_registry or default_tool_registry
        
        # Initialize LLM
        self.llm = ChatOpenAI(
            model=self.config.model,
            temperature=self.config.temperature,
            max_tokens=self.config.max_tokens,
            api_key=api_key or os.getenv("OPENAI_API_KEY"),
        )
        
        # Prepare system message with tool information
        tools_info = self._format_tools_info()
        self.system_message = (self.config.system_message or self.DEFAULT_SYSTEM_MESSAGE).format(
            tools_info=tools_info
        )
    
    def _format_tools_info(self) -> str:
        """Format tool information for the system message."""
        tools_info = []
        for tool_name, tool_info in self.tool_registry.get_tools_info().items():
            tools_info.append(f"- {tool_name}: {tool_info['description']}")
        return "\n".join(tools_info)
    
    async def run(self, user_input: str, session_id: Optional[str] = None) -> AgentResponse:
        """Run the agent on a user input.
        
        Args:
            user_input: The user's request or question
            session_id: Optional session identifier
            
        Returns:
            AgentResponse with the agent's result
        """
        start_time = time.time()
        session_id = session_id or str(uuid.uuid4())
        
        # Initialize agent state
        state = AgentState(
            session_id=session_id,
            user_input=user_input,
            max_iterations=self.config.max_iterations
        )
        
        try:
            # Main reasoning loop
            while not state.is_complete and not state.is_max_iterations_reached():
                state.increment_iteration()
                
                if self.config.verbose:
                    print(f"Iteration {state.iteration_count}: {state.current_step}")
                
                # Get next action from LLM
                response = await self._get_llm_response(state)
                
                # Parse response and update state
                await self._process_llm_response(state, response)
                
                if state.is_complete:
                    break
            
            # Handle case where max iterations reached without completion
            if not state.is_complete:
                state.complete_with_answer(
                    "I've reached the maximum number of reasoning steps without completing the task. "
                    "Please try rephrasing your request or breaking it into smaller parts."
                )
            
            # Create response
            execution_time = time.time() - start_time
            tools_used = list(set(
                tool_call.tool_name 
                for thought in state.thoughts 
                for tool_call in thought.tool_calls
            ))
            
            return AgentResponse(
                state=state,
                answer=state.final_answer or "No answer provided",
                reasoning_chain=state.thoughts,
                tools_used=tools_used,
                total_iterations=state.iteration_count,
                execution_time=execution_time,
                success=True
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            return AgentResponse(
                state=state,
                answer=f"Error occurred: {str(e)}",
                reasoning_chain=state.thoughts,
                tools_used=[],
                total_iterations=state.iteration_count,
                execution_time=execution_time,
                success=False,
                error=str(e)
            )
    
    def run_sync(self, user_input: str, session_id: Optional[str] = None) -> AgentResponse:
        """Synchronous wrapper for run method."""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.run(user_input, session_id))
        except RuntimeError:
            return asyncio.run(self.run(user_input, session_id))
    
    async def _get_llm_response(self, state: AgentState) -> str:
        """Get response from the LLM based on current state."""
        messages = [SystemMessage(content=self.system_message)]
        
        # Add user input
        messages.append(HumanMessage(content=f"User request: {state.user_input}"))
        
        # Add conversation history
        for thought in state.thoughts:
            if thought.step == AgentStep.THINK:
                messages.append(AIMessage(content=f"THINKING: {thought.content}"))
            elif thought.step == AgentStep.ACT:
                messages.append(AIMessage(content=f"ACTION: {thought.content}"))
                # Add tool calls
                for tool_call in thought.tool_calls:
                    tool_call_msg = f"TOOL_CALL: {json.dumps({'tool_name': tool_call.tool_name, 'arguments': tool_call.arguments})}"
                    messages.append(AIMessage(content=tool_call_msg))
            elif thought.step == AgentStep.OBSERVE:
                messages.append(AIMessage(content=f"OBSERVATION: {thought.content}"))
                # Add tool results
                for observation in thought.observations:
                    result_summary = f"Tool '{observation.tool_call.tool_name}' result: {observation.result}"
                    messages.append(HumanMessage(content=result_summary))
        
        # Get response from LLM
        response = await self.llm.ainvoke(messages)
        return response.content
    
    async def _process_llm_response(self, state: AgentState, response: str) -> None:
        """Process the LLM response and update agent state."""
        response = response.strip()
        
        if response.startswith("FINAL_ANSWER:"):
            # Agent is providing final answer
            answer = response[len("FINAL_ANSWER:"):].strip()
            state.complete_with_answer(answer)
            
        elif "TOOL_CALL:" in response:
            # Agent wants to use a tool
            await self._handle_tool_call(state, response)
            
        else:
            # Agent is thinking/reasoning
            state.add_thought(AgentStep.THINK, response)
    
    async def _handle_tool_call(self, state: AgentState, response: str) -> None:
        """Handle tool calls from the agent."""
        try:
            # Extract tool call from response
            tool_call_start = response.find("TOOL_CALL:")
            tool_call_json = response[tool_call_start + len("TOOL_CALL:"):].strip()
            
            # Parse JSON
            tool_call_data = json.loads(tool_call_json)
            tool_name = tool_call_data["tool_name"]
            arguments = tool_call_data.get("arguments", {})
            
            # Create tool call
            tool_call = state.add_tool_call(tool_name, arguments)
            
            if self.config.verbose:
                print(f"Calling tool: {tool_name} with args: {arguments}")
            
            # Execute tool
            tool_result = await self.tool_registry.execute_tool(tool_call)
            
            # Add observation
            state.add_observation(tool_result)
            
            # Add observation thought
            if tool_result.success:
                observation_content = f"Tool '{tool_name}' executed successfully. Result: {tool_result.result}"
            else:
                observation_content = f"Tool '{tool_name}' failed with error: {tool_result.error}"
            
            state.add_thought(AgentStep.OBSERVE, observation_content)
            
        except Exception as e:
            # Handle tool call parsing/execution errors
            error_msg = f"Error processing tool call: {str(e)}"
            state.add_thought(AgentStep.OBSERVE, error_msg)
            
            if self.config.verbose:
                print(f"Tool call error: {error_msg}")


class AgentClient:
    """High-level client for the React Agent."""
    
    def __init__(
        self,
        model: str = "gpt-4",
        temperature: float = 0.1,
        max_tokens: Optional[int] = None,
        max_iterations: int = 10,
        verbose: bool = False,
        api_key: Optional[str] = None,
        system_message: Optional[str] = None,
        tool_registry: Optional[ToolRegistry] = None,
    ):
        """Initialize the Agent Client.
        
        Args:
            model: OpenAI model to use
            temperature: Sampling temperature
            max_tokens: Maximum tokens per response
            max_iterations: Maximum reasoning iterations
            verbose: Enable verbose logging
            api_key: OpenAI API key
            system_message: Custom system message
            tool_registry: Custom tool registry
        """
        config = AgentConfig(
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            max_iterations=max_iterations,
            verbose=verbose,
            system_message=system_message
        )
        
        self.agent = ReactAgent(
            config=config,
            tool_registry=tool_registry,
            api_key=api_key
        )
    
    def run(self, user_input: str, session_id: Optional[str] = None) -> AgentResponse:
        """Run the agent on user input.
        
        Args:
            user_input: The user's request or question
            session_id: Optional session identifier
            
        Returns:
            AgentResponse with the result
        """
        return self.agent.run_sync(user_input, session_id)
    
    async def run_async(self, user_input: str, session_id: Optional[str] = None) -> AgentResponse:
        """Async version of run method."""
        return await self.agent.run(user_input, session_id)
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names."""
        return self.agent.tool_registry.list_tools()
    
    def get_tools_info(self) -> Dict[str, Dict[str, Any]]:
        """Get detailed information about available tools."""
        return self.agent.tool_registry.get_tools_info()
    
    def add_tool(self, tool) -> None:
        """Add a custom tool to the agent.
        
        Args:
            tool: Tool instance to add
        """
        self.agent.tool_registry.register_tool(tool)