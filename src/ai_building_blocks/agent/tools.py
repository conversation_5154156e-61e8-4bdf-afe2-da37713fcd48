"""Tool system for the agent component."""

import json
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Callable, Type
from datetime import datetime

from pydantic import BaseModel, Field
from .models import ToolCall, ToolResult


class ToolSchema(BaseModel):
    """Schema definition for a tool."""
    
    name: str = Field(..., description="Name of the tool")
    description: str = Field(..., description="Description of what the tool does")
    parameters: Dict[str, Any] = Field(default_factory=dict, description="JSON schema for tool parameters")
    required: List[str] = Field(default_factory=list, description="List of required parameter names")


class Tool(ABC):
    """Abstract base class for agent tools."""
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    @abstractmethod
    def get_schema(self) -> ToolSchema:
        """Get the JSON schema for this tool."""
        pass
    
    @abstractmethod
    async def execute(self, **kwargs) -> Any:
        """Execute the tool with given parameters."""
        pass
    
    def execute_sync(self, **kwargs) -> Any:
        """Synchronous execution wrapper."""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.execute(**kwargs))
        except RuntimeError:
            # No event loop running, create a new one
            return asyncio.run(self.execute(**kwargs))


class CalculatorTool(Tool):
    """Simple calculator tool for mathematical operations."""
    
    def __init__(self):
        super().__init__(
            name="calculator",
            description="Perform basic mathematical calculations (addition, subtraction, multiplication, division)"
        )
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "Mathematical expression to evaluate (e.g., '2 + 3 * 4')"
                    }
                }
            },
            required=["expression"]
        )
    
    async def execute(self, expression: str) -> Dict[str, Any]:
        """Execute mathematical calculation."""
        try:
            # Safe evaluation of mathematical expressions
            allowed_chars = set('0123456789+-*/()., ')
            if not all(c in allowed_chars for c in expression):
                raise ValueError("Invalid characters in expression")
            
            result = eval(expression)
            return {
                "result": result,
                "expression": expression,
                "success": True
            }
        except Exception as e:
            return {
                "error": str(e),
                "expression": expression,
                "success": False
            }


class WebSearchTool(Tool):
    """Mock web search tool for demonstration."""
    
    def __init__(self):
        super().__init__(
            name="web_search",
            description="Search the web for information on a given topic"
        )
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "query": {
                        "type": "string",
                        "description": "Search query to look up"
                    },
                    "max_results": {
                        "type": "integer",
                        "description": "Maximum number of results to return",
                        "default": 5
                    }
                }
            },
            required=["query"]
        )
    
    async def execute(self, query: str, max_results: int = 5) -> Dict[str, Any]:
        """Execute web search (mock implementation)."""
        # This is a mock implementation
        # In a real implementation, you would integrate with a search API
        mock_results = [
            {
                "title": f"Result 1 for '{query}'",
                "url": "https://example1.com",
                "snippet": f"This is a mock search result about {query}..."
            },
            {
                "title": f"Result 2 for '{query}'",
                "url": "https://example2.com", 
                "snippet": f"Another mock result containing information about {query}..."
            }
        ]
        
        return {
            "query": query,
            "results": mock_results[:max_results],
            "total_results": len(mock_results),
            "success": True
        }


class FileTool(Tool):
    """Tool for reading and writing files."""
    
    def __init__(self):
        super().__init__(
            name="file_operations",
            description="Read from or write to text files"
        )
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "operation": {
                        "type": "string",
                        "enum": ["read", "write"],
                        "description": "Operation to perform: 'read' or 'write'"
                    },
                    "filename": {
                        "type": "string",
                        "description": "Path to the file"
                    },
                    "content": {
                        "type": "string",
                        "description": "Content to write (only for write operation)"
                    }
                }
            },
            required=["operation", "filename"]
        )
    
    async def execute(self, operation: str, filename: str, content: str = None) -> Dict[str, Any]:
        """Execute file operation."""
        try:
            if operation == "read":
                with open(filename, 'r', encoding='utf-8') as f:
                    file_content = f.read()
                return {
                    "operation": "read",
                    "filename": filename,
                    "content": file_content,
                    "success": True
                }
            elif operation == "write":
                if content is None:
                    raise ValueError("Content is required for write operation")
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(content)
                return {
                    "operation": "write",
                    "filename": filename,
                    "bytes_written": len(content),
                    "success": True
                }
            else:
                raise ValueError(f"Unknown operation: {operation}")
                
        except Exception as e:
            return {
                "operation": operation,
                "filename": filename,
                "error": str(e),
                "success": False
            }


class ToolRegistry:
    """Registry for managing available tools."""
    
    def __init__(self):
        self._tools: Dict[str, Tool] = {}
        self._register_default_tools()
    
    def _register_default_tools(self):
        """Register default tools."""
        default_tools = [
            CalculatorTool(),
            WebSearchTool(),
            FileTool()
        ]
        
        for tool in default_tools:
            self.register_tool(tool)
    
    def register_tool(self, tool: Tool) -> None:
        """Register a new tool."""
        self._tools[tool.name] = tool
    
    def get_tool(self, name: str) -> Optional[Tool]:
        """Get a tool by name."""
        return self._tools.get(name)
    
    def list_tools(self) -> List[str]:
        """List all registered tool names."""
        return list(self._tools.keys())
    
    def get_all_schemas(self) -> List[ToolSchema]:
        """Get schemas for all registered tools."""
        return [tool.get_schema() for tool in self._tools.values()]
    
    def get_tools_info(self) -> Dict[str, Dict[str, Any]]:
        """Get information about all registered tools."""
        return {
            name: {
                "name": tool.name,
                "description": tool.description,
                "schema": tool.get_schema().dict()
            }
            for name, tool in self._tools.items()
        }
    
    async def execute_tool(self, tool_call: ToolCall) -> ToolResult:
        """Execute a tool call and return the result."""
        start_time = time.time()
        
        tool = self.get_tool(tool_call.tool_name)
        if not tool:
            return ToolResult(
                tool_call=tool_call,
                result=None,
                success=False,
                error=f"Tool '{tool_call.tool_name}' not found",
                execution_time=time.time() - start_time
            )
        
        try:
            result = await tool.execute(**tool_call.arguments)
            return ToolResult(
                tool_call=tool_call,
                result=result,
                success=True,
                execution_time=time.time() - start_time
            )
        except Exception as e:
            return ToolResult(
                tool_call=tool_call,
                result=None,
                success=False,
                error=str(e),
                execution_time=time.time() - start_time
            )
    
    def execute_tool_sync(self, tool_call: ToolCall) -> ToolResult:
        """Synchronous wrapper for tool execution."""
        import asyncio
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(self.execute_tool(tool_call))
        except RuntimeError:
            return asyncio.run(self.execute_tool(tool_call))


# Create a global tool registry instance
default_tool_registry = ToolRegistry()