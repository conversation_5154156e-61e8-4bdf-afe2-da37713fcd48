"""Data models for the agent component."""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Union, Callable

from pydantic import BaseModel, Field, ConfigDict


class AgentStep(str, Enum):
    """Agent execution steps."""
    
    THINK = "think"
    ACT = "act" 
    OBSERVE = "observe"
    FINAL = "final"


class ToolCall(BaseModel):
    """Represents a tool call made by the agent."""
    
    tool_name: str = Field(..., description="Name of the tool called")
    arguments: Dict[str, Any] = Field(default_factory=dict, description="Arguments passed to the tool")
    call_id: Optional[str] = Field(default=None, description="Unique identifier for this tool call")
    timestamp: datetime = Field(default_factory=datetime.now, description="When the tool was called")


class ToolResult(BaseModel):
    """Result from a tool execution."""
    
    tool_call: ToolCall = Field(..., description="The original tool call")
    result: Any = Field(..., description="Result returned by the tool")
    success: bool = Field(..., description="Whether the tool execution was successful")
    error: Optional[str] = Field(default=None, description="Error message if execution failed")
    execution_time: Optional[float] = Field(default=None, description="Time taken to execute the tool in seconds")


class AgentThought(BaseModel):
    """Represents the agent's reasoning step."""
    
    step: AgentStep = Field(..., description="The type of step this thought represents")
    content: str = Field(..., description="The agent's thought or reasoning")
    tool_calls: List[ToolCall] = Field(default_factory=list, description="Tools the agent decided to call")
    observations: List[ToolResult] = Field(default_factory=list, description="Results from tool executions")
    timestamp: datetime = Field(default_factory=datetime.now, description="When this thought occurred")


class AgentState(BaseModel):
    """Represents the current state of the agent."""
    
    session_id: str = Field(..., description="Unique identifier for the agent session")
    user_input: str = Field(..., description="The user's input/request")
    thoughts: List[AgentThought] = Field(default_factory=list, description="Chain of agent thoughts and actions")
    current_step: AgentStep = Field(default=AgentStep.THINK, description="Current step in the reasoning process")
    max_iterations: int = Field(default=10, description="Maximum number of reasoning iterations")
    iteration_count: int = Field(default=0, description="Current iteration number")
    is_complete: bool = Field(default=False, description="Whether the agent has completed the task")
    final_answer: Optional[str] = Field(default=None, description="The agent's final answer")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional state metadata")
    
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    def add_thought(self, step: AgentStep, content: str) -> AgentThought:
        """Add a new thought to the agent's reasoning chain."""
        thought = AgentThought(step=step, content=content)
        self.thoughts.append(thought)
        self.current_step = step
        return thought
    
    def add_tool_call(self, tool_name: str, arguments: Dict[str, Any]) -> ToolCall:
        """Add a tool call to the current thought."""
        if not self.thoughts:
            self.add_thought(AgentStep.ACT, f"Calling tool: {tool_name}")
        
        current_thought = self.thoughts[-1]
        tool_call = ToolCall(tool_name=tool_name, arguments=arguments)
        current_thought.tool_calls.append(tool_call)
        return tool_call
    
    def add_observation(self, tool_result: ToolResult) -> None:
        """Add a tool result observation to the current thought."""
        if not self.thoughts:
            self.add_thought(AgentStep.OBSERVE, "Processing tool result")
        
        current_thought = self.thoughts[-1]
        current_thought.observations.append(tool_result)
    
    def increment_iteration(self) -> None:
        """Increment the iteration counter."""
        self.iteration_count += 1
    
    def is_max_iterations_reached(self) -> bool:
        """Check if maximum iterations have been reached."""
        return self.iteration_count >= self.max_iterations
    
    def complete_with_answer(self, answer: str) -> None:
        """Mark the agent as complete with a final answer."""
        self.final_answer = answer
        self.is_complete = True
        self.current_step = AgentStep.FINAL
        self.add_thought(AgentStep.FINAL, f"Task completed. Final answer: {answer}")


class AgentResponse(BaseModel):
    """Response from the agent after processing."""
    
    state: AgentState = Field(..., description="Final state of the agent")
    answer: str = Field(..., description="The agent's answer to the user's request")
    reasoning_chain: List[AgentThought] = Field(..., description="Complete chain of reasoning")
    tools_used: List[str] = Field(default_factory=list, description="Names of tools used during execution")
    total_iterations: int = Field(..., description="Total number of reasoning iterations")
    execution_time: Optional[float] = Field(default=None, description="Total execution time in seconds")
    success: bool = Field(default=True, description="Whether the agent successfully completed the task")
    error: Optional[str] = Field(default=None, description="Error message if execution failed")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional response metadata")
    
    @property
    def tool_calls_count(self) -> int:
        """Get total number of tool calls made."""
        return sum(len(thought.tool_calls) for thought in self.reasoning_chain)
    
    @property
    def successful_tool_calls(self) -> int:
        """Get number of successful tool calls."""
        count = 0
        for thought in self.reasoning_chain:
            for observation in thought.observations:
                if observation.success:
                    count += 1
        return count
    
    def get_tool_usage_summary(self) -> Dict[str, int]:
        """Get summary of tool usage."""
        tool_usage = {}
        for thought in self.reasoning_chain:
            for tool_call in thought.tool_calls:
                tool_usage[tool_call.tool_name] = tool_usage.get(tool_call.tool_name, 0) + 1
        return tool_usage


class AgentConfig(BaseModel):
    """Configuration for the agent."""
    
    model: str = Field(default="gpt-4", description="LLM model to use")
    temperature: float = Field(default=0.1, description="Sampling temperature")
    max_tokens: Optional[int] = Field(default=None, description="Maximum tokens per response")
    max_iterations: int = Field(default=10, description="Maximum reasoning iterations")
    verbose: bool = Field(default=False, description="Enable verbose logging")
    system_message: Optional[str] = Field(default=None, description="System message for the agent")
    
    model_config = ConfigDict(frozen=True)