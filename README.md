# AI Building Blocks

一个用于快速构建AI产品原型的模块化Python包，提供对话、检索增强生成(RAG)和智能代理三大核心组件。

## 🚀 特性

- **💬 Chat组件**: 基于LangChain和OpenAI的简单对话接口
- **📚 RAG组件**: 具有文档索引和向量搜索的检索增强生成系统
- **🤖 Agent组件**: 基于React模式的智能代理，支持工具使用和多步推理
- **🔧 工具系统**: 内置计算器、网络搜索、文件操作等工具
- **📊 状态管理**: 完整的对话历史和推理过程跟踪
- **🎯 简单易用**: 统一的API设计，快速上手

## 📦 安装

### 安装uv

首先安装uv（如果尚未安装）：

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 或使用包管理器
# macOS
brew install uv

# 验证安装
uv --version
```

### 从源码安装

```bash
git clone https://github.com/niutao/ai-building-blocks.git
cd ai-building-blocks

# 使用uv安装依赖
uv sync

# 安装开发依赖
uv sync --extra dev

# 或者使用pip方式（在uv虚拟环境中）
uv pip install -e ".[dev]"
```

### 环境要求

- Python 3.11+
- [uv](https://docs.astral.sh/uv/) (推荐的Python包管理器)
- OpenAI API密钥

## ⚙️ 配置

创建`.env`文件并添加你的OpenAI API密钥：

```bash
OPENAI_API_KEY=你的_openai_api_密钥
```

## 🔧 快速开始

### 📦 包管理

本项目使用uv作为包管理器，提供快速的依赖解析和安装：

```bash
# 同步依赖（安装所有依赖）
uv sync

# 添加新依赖
uv add <package-name>

# 添加开发依赖
uv add --dev <package-name>

# 运行Python脚本
uv run python script.py

# 运行pytest
uv run pytest

# 激活虚拟环境
source .venv/bin/activate  # Linux/macOS
# 或
.venv\Scripts\activate     # Windows
```

### Chat组件 - 简单对话

```python
from ai_building_blocks import ChatClient

# 创建对话客户端
chat = ChatClient(
    model="gpt-3.5-turbo",
    temperature=0.7,
    system_message="你是一个有用的助手"
)

# 发送消息
response = chat.send_message("你好！")
print(response.content)

# 查看对话历史
history = chat.get_history()
print(f"对话包含 {len(history.messages)} 条消息")
```

### RAG组件 - 知识库问答

```python
from ai_building_blocks import RAGClient, Document

# 创建RAG客户端
rag = RAGClient(
    model="gpt-3.5-turbo",
    temperature=0.1  # 低温度获得更准确的回答
)

# 添加文档到知识库
documents = [
    Document(
        content="Python是一种高级、解释型编程语言，具有动态语义。",
        source="python_intro.txt"
    ),
    Document(
        content="机器学习是人工智能的一个子集，它为系统提供自动学习和改进的能力。",
        source="ml_intro.txt"
    )
]

rag.add_documents(documents)

# 查询知识库
response = rag.query("什么是Python？")
print(f"答案: {response.answer}")
print(f"来源: {response.sources}")
```

### Agent组件 - 智能代理

```python
from ai_building_blocks import AgentClient

# 创建智能代理
agent = AgentClient(
    model="gpt-3.5-turbo",
    temperature=0.1,
    max_iterations=8,
    verbose=True  # 显示推理过程
)

# 执行复杂任务
response = agent.run("计算半径为5的圆的面积，然后将结果保存到文件中")

print(f"答案: {response.answer}")
print(f"使用的工具: {response.tools_used}")
print(f"推理步骤: {response.total_iterations}")

# 查看推理链
for i, thought in enumerate(response.reasoning_chain, 1):
    print(f"{i}. [{thought.step.value.upper()}] {thought.content[:100]}...")
```

## 📚 组件详解

### 💬 Chat组件

Chat组件提供简单而强大的对话功能：

- **对话历史管理**: 自动维护对话上下文
- **系统消息支持**: 设置AI助手的角色和行为
- **批量消息处理**: 一次发送多条消息
- **灵活配置**: 支持不同模型和参数

```python
# 高级用法
chat = ChatClient(model="gpt-4", temperature=0.5)

# 设置系统消息
chat.set_system_message("你是一个专业的Python编程助手")

# 批量发送消息
messages = ["什么是装饰器？", "给我一个例子", "还有其他用法吗？"]
responses = chat.send_messages(messages)

# 清空历史但保留系统消息
chat.clear_history(keep_system=True)
```

### 📚 RAG组件

RAG组件实现了完整的检索增强生成流程：

- **文档管理**: 支持多种格式的文档添加
- **向量搜索**: 基于语义相似度的智能检索
- **上下文生成**: 自动整合相关文档片段
- **灵活配置**: 可调节检索数量和相似度阈值

```python
from ai_building_blocks import RAGClient, VectorStoreConfig

# 自定义配置
config = VectorStoreConfig(
    chunk_size=500,      # 文档分块大小
    chunk_overlap=50,    # 分块重叠
    top_k=3,            # 检索最相关的3个片段
    score_threshold=0.1  # 相似度阈值
)

rag = RAGClient(vector_store_config=config)

# 添加纯文本
texts = ["FastAPI是一个现代、快速的Web框架", "Docker是容器化平台"]
rag.add_texts(texts, sources=["fastapi.txt", "docker.txt"])

# 搜索文档（不生成答案）
docs = rag.search_documents("Web框架", top_k=2)
for doc in docs:
    print(f"相似度: {doc.score:.3f}, 来源: {doc.document.source}")
```

### 🤖 Agent组件

Agent组件基于React（推理和行动）模式，能够进行多步推理和工具使用：

- **React模式**: 思考 → 行动 → 观察 → 重复
- **工具系统**: 内置多种工具，支持自定义工具
- **状态管理**: 完整的推理过程跟踪
- **错误处理**: 优雅的错误恢复机制

```python
# 查看可用工具
print("可用工具:", agent.get_available_tools())
print("工具详情:", agent.get_tools_info())

# 复杂任务示例
response = agent.run("""
我需要：
1. 计算1000美元在5%年利率下，复利3年的最终金额
2. 搜索复利的相关信息
3. 将计算结果和解释保存到文件中
""")

# 查看工具使用统计
usage_summary = response.get_tool_usage_summary()
print("工具使用统计:", usage_summary)
```

#### 自定义工具

```python
from ai_building_blocks.agent.tools import Tool, ToolSchema

class WeatherTool(Tool):
    def __init__(self):
        super().__init__("weather", "获取城市天气信息")
    
    def get_schema(self) -> ToolSchema:
        return ToolSchema(
            name=self.name,
            description=self.description,
            parameters={
                "type": "object",
                "properties": {
                    "city": {"type": "string", "description": "城市名称"}
                }
            },
            required=["city"]
        )
    
    async def execute(self, city: str):
        # 这里实现天气查询逻辑
        return {"city": city, "weather": "晴天", "temperature": "25°C"}

# 添加自定义工具
agent.add_tool(WeatherTool())
```

## 📁 项目结构

```
ai_building_blocks/
├── src/ai_building_blocks/
│   ├── chat/           # 对话组件
│   │   ├── client.py   # ChatClient实现
│   │   └── models.py   # 数据模型
│   ├── rag/            # RAG组件
│   │   ├── client.py   # RAGClient实现
│   │   ├── models.py   # 数据模型
│   │   └── vectorstore.py # 向量存储
│   └── agent/          # Agent组件
│       ├── client.py   # AgentClient实现
│       ├── models.py   # 数据模型
│       └── tools.py    # 工具系统
├── examples/           # 使用示例
├── tests/             # 单元测试
└── docs/              # 文档
```

## 🛠️ 开发

### 环境设置

```bash
# 克隆仓库
git clone https://github.com/niutao/ai-building-blocks.git
cd ai-building-blocks

# 使用uv同步所有依赖（包括开发依赖）
uv sync --extra dev

# 或分步安装
uv sync                          # 基础依赖
uv sync --extra dev             # 开发依赖

# 安装pre-commit钩子
pre-commit install
```

### 运行测试

```bash
# 运行所有测试
uv run pytest

# 运行特定组件测试
uv run pytest tests/chat/
uv run pytest tests/rag/
uv run pytest tests/agent/

# 详细输出
uv run pytest -v
```

### 代码质量

```bash
# 代码格式化
uv run black .
uv run isort .

# 类型检查
uv run mypy src/

# 运行pre-commit检查
pre-commit run --all-files
```

## 📖 示例

查看`examples/`目录中的完整示例：

- `chat_example.py` - Chat组件使用示例
- `rag_example.py` - RAG组件使用示例  
- `agent_example.py` - Agent组件使用示例

运行示例：

```bash
uv run python examples/chat_example.py
uv run python examples/rag_example.py
uv run python examples/agent_example.py
```

## 🤝 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [项目主页](https://github.com/niutao/ai-building-blocks)
- [问题反馈](https://github.com/niutao/ai-building-blocks/issues)
- [LangChain文档](https://python.langchain.com/)
- [OpenAI API文档](https://platform.openai.com/docs)

## ⭐ 支持项目

如果这个项目对你有帮助，请给它一个星标 ⭐！