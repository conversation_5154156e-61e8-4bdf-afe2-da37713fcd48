"""Tests for chat models."""

import pytest
from datetime import datetime

from ai_building_blocks.chat.models import <PERSON>t<PERSON>essage, ChatResponse, ChatHistory, MessageRole


class TestChatMessage:
    """Test ChatMessage model."""
    
    def test_create_message(self):
        """Test creating a chat message."""
        message = ChatMessage(role=MessageRole.USER, content="Hello, world!")
        
        assert message.role == MessageRole.USER
        assert message.content == "Hello, world!"
        assert isinstance(message.timestamp, datetime)
        assert message.metadata is None
    
    def test_create_message_with_metadata(self):
        """Test creating a message with metadata."""
        metadata = {"source": "test", "priority": "high"}
        message = ChatMessage(
            role=MessageRole.ASSISTANT,
            content="Hello back!",
            metadata=metadata
        )
        
        assert message.role == MessageRole.ASSISTANT
        assert message.content == "Hello back!"
        assert message.metadata == metadata


class TestChatResponse:
    """Test ChatResponse model."""
    
    def test_create_response(self):
        """Test creating a chat response."""
        message = ChatMessage(role=MessageRole.ASSISTANT, content="Hello!")
        response = ChatResponse(message=message, model="gpt-4")
        
        assert response.message == message
        assert response.model == "gpt-4"
        assert response.content == "Hello!"


class TestChatHistory:
    """Test ChatHistory model."""
    
    def test_empty_history(self):
        """Test creating empty history."""
        history = ChatHistory()
        assert len(history.messages) == 0
        assert history.session_id is None
    
    def test_add_message(self):
        """Test adding messages to history."""
        history = ChatHistory()
        history.add_message(MessageRole.USER, "Hello")
        history.add_message(MessageRole.ASSISTANT, "Hi there!")
        
        assert len(history.messages) == 2
        assert history.messages[0].role == MessageRole.USER
        assert history.messages[0].content == "Hello"
        assert history.messages[1].role == MessageRole.ASSISTANT
        assert history.messages[1].content == "Hi there!"
    
    def test_get_messages_for_api(self):
        """Test converting messages for API format."""
        history = ChatHistory()
        history.add_message(MessageRole.USER, "Hello")
        history.add_message(MessageRole.ASSISTANT, "Hi!")
        
        api_messages = history.get_messages_for_api()
        expected = [
            {"role": "user", "content": "Hello"},
            {"role": "assistant", "content": "Hi!"}
        ]
        
        assert api_messages == expected
    
    def test_clear_history(self):
        """Test clearing history."""
        history = ChatHistory()
        history.add_message(MessageRole.USER, "Hello")
        history.add_message(MessageRole.ASSISTANT, "Hi!")
        
        assert len(history.messages) == 2
        
        history.clear()
        assert len(history.messages) == 0
    
    def test_get_last_n_messages(self):
        """Test getting last n messages."""
        history = ChatHistory()
        for i in range(5):
            history.add_message(MessageRole.USER, f"Message {i}")
        
        last_2 = history.get_last_n_messages(2)
        assert len(last_2) == 2
        assert last_2[0].content == "Message 3"
        assert last_2[1].content == "Message 4"
        
        # Test edge cases
        assert len(history.get_last_n_messages(0)) == 0
        assert len(history.get_last_n_messages(10)) == 5  # More than available
