"""Tests for RAG models."""

import pytest
from datetime import datetime

from ai_building_blocks.rag.models import (
    Document, 
    RetrievedDocument, 
    RAGResponse, 
    VectorStoreConfig,
    IndexStats
)


class TestDocument:
    """Test Document model."""
    
    def test_create_document(self):
        """Test creating a document."""
        doc = Document(content="This is a test document.")
        
        assert doc.content == "This is a test document."
        assert doc.metadata == {}
        assert doc.doc_id is None
        assert doc.source is None
        assert isinstance(doc.created_at, datetime)
    
    def test_create_document_with_metadata(self):
        """Test creating a document with metadata."""
        metadata = {"author": "test", "category": "example"}
        doc = Document(
            content="Test content",
            metadata=metadata,
            doc_id="test-123",
            source="test.txt"
        )
        
        assert doc.content == "Test content"
        assert doc.metadata == metadata
        assert doc.doc_id == "test-123"
        assert doc.source == "test.txt"
    
    def test_document_str(self):
        """Test document string representation."""
        doc = Document(
            content="Short content",
            doc_id="test-123",
            source="test.txt"
        )
        
        str_repr = str(doc)
        assert "test-123" in str_repr
        assert "test.txt" in str_repr
        assert "Short content" in str_repr


class TestRetrievedDocument:
    """Test RetrievedDocument model."""
    
    def test_create_retrieved_document(self):
        """Test creating a retrieved document."""
        doc = Document(content="Test content")
        retrieved = RetrievedDocument(
            document=doc,
            score=0.85,
            rank=1
        )
        
        assert retrieved.document == doc
        assert retrieved.score == 0.85
        assert retrieved.rank == 1
        assert retrieved.content == "Test content"
        assert retrieved.metadata == {}


class TestRAGResponse:
    """Test RAGResponse model."""
    
    def test_create_rag_response(self):
        """Test creating a RAG response."""
        doc = Document(content="Context document", source="test.txt")
        retrieved = RetrievedDocument(document=doc, score=0.9, rank=1)
        
        response = RAGResponse(
            answer="This is the answer",
            retrieved_documents=[retrieved],
            query="What is the question?",
            model="gpt-3.5-turbo"
        )
        
        assert response.answer == "This is the answer"
        assert len(response.retrieved_documents) == 1
        assert response.query == "What is the question?"
        assert response.model == "gpt-3.5-turbo"
    
    def test_rag_response_sources(self):
        """Test getting sources from RAG response."""
        doc1 = Document(content="Content 1", source="file1.txt")
        doc2 = Document(content="Content 2", source="file2.txt")
        doc3 = Document(content="Content 3", source="file1.txt")  # Duplicate source
        
        retrieved_docs = [
            RetrievedDocument(document=doc1, score=0.9, rank=1),
            RetrievedDocument(document=doc2, score=0.8, rank=2),
            RetrievedDocument(document=doc3, score=0.7, rank=3),
        ]
        
        response = RAGResponse(
            answer="Answer",
            retrieved_documents=retrieved_docs,
            query="Question"
        )
        
        sources = response.sources
        assert len(sources) == 2  # Unique sources only
        assert "file1.txt" in sources
        assert "file2.txt" in sources
    
    def test_rag_response_context(self):
        """Test getting context from RAG response."""
        doc1 = Document(content="First document content")
        doc2 = Document(content="Second document content")
        
        retrieved_docs = [
            RetrievedDocument(document=doc1, score=0.9, rank=1),
            RetrievedDocument(document=doc2, score=0.8, rank=2),
        ]
        
        response = RAGResponse(
            answer="Answer",
            retrieved_documents=retrieved_docs,
            query="Question"
        )
        
        context = response.context
        expected = "First document content\n\nSecond document content"
        assert context == expected


class TestVectorStoreConfig:
    """Test VectorStoreConfig model."""
    
    def test_default_config(self):
        """Test default configuration."""
        config = VectorStoreConfig()
        
        assert config.embedding_model == "text-embedding-ada-002"
        assert config.chunk_size == 1000
        assert config.chunk_overlap == 200
        assert config.top_k == 5
        assert config.score_threshold == 0.0
    
    def test_custom_config(self):
        """Test custom configuration."""
        config = VectorStoreConfig(
            embedding_model="text-embedding-3-small",
            chunk_size=500,
            chunk_overlap=100,
            top_k=10,
            score_threshold=0.5
        )
        
        assert config.embedding_model == "text-embedding-3-small"
        assert config.chunk_size == 500
        assert config.chunk_overlap == 100
        assert config.top_k == 10
        assert config.score_threshold == 0.5


class TestIndexStats:
    """Test IndexStats model."""
    
    def test_create_index_stats(self):
        """Test creating index statistics."""
        stats = IndexStats(
            total_documents=100,
            total_chunks=500,
            embedding_model="text-embedding-ada-002",
            index_size_mb=25.5
        )
        
        assert stats.total_documents == 100
        assert stats.total_chunks == 500
        assert stats.embedding_model == "text-embedding-ada-002"
        assert stats.index_size_mb == 25.5
        assert isinstance(stats.last_updated, datetime)
    
    def test_index_stats_str(self):
        """Test index stats string representation."""
        stats = IndexStats(
            total_documents=10,
            total_chunks=50,
            embedding_model="test-model",
            index_size_mb=5.0
        )
        
        str_repr = str(stats)
        assert "docs=10" in str_repr
        assert "chunks=50" in str_repr
        assert "model=test-model" in str_repr
        assert "size=5.0MB" in str_repr
