"""Tests for the agent models."""

import pytest
from datetime import datetime

from ai_building_blocks.agent.models import (
    AgentState, AgentStep, AgentThought, <PERSON>lCall, <PERSON><PERSON>Result, AgentResponse, AgentConfig
)


class TestAgentState:
    """Test cases for AgentState model."""
    
    def test_agent_state_creation(self):
        """Test basic agent state creation."""
        state = AgentState(
            session_id="test-session",
            user_input="Test input"
        )
        
        assert state.session_id == "test-session"
        assert state.user_input == "Test input"
        assert state.current_step == AgentStep.THINK
        assert state.iteration_count == 0
        assert not state.is_complete
        assert state.final_answer is None
        assert len(state.thoughts) == 0
    
    def test_add_thought(self):
        """Test adding thoughts to agent state."""
        state = AgentState(session_id="test", user_input="test")
        
        thought = state.add_thought(AgentStep.THINK, "I need to analyze this problem")
        
        assert len(state.thoughts) == 1
        assert state.thoughts[0] == thought
        assert state.current_step == AgentStep.THINK
        assert thought.step == AgentStep.THINK
        assert thought.content == "I need to analyze this problem"
    
    def test_add_tool_call(self):
        """Test adding tool calls to agent state."""
        state = AgentState(session_id="test", user_input="test")
        
        tool_call = state.add_tool_call("calculator", {"expression": "2+2"})
        
        assert len(state.thoughts) == 1  # Should create a thought
        assert len(state.thoughts[0].tool_calls) == 1
        assert tool_call.tool_name == "calculator"
        assert tool_call.arguments == {"expression": "2+2"}
    
    def test_increment_iteration(self):
        """Test iteration counter increment."""
        state = AgentState(session_id="test", user_input="test")
        
        assert state.iteration_count == 0
        state.increment_iteration()
        assert state.iteration_count == 1
    
    def test_max_iterations_check(self):
        """Test max iterations check."""
        state = AgentState(session_id="test", user_input="test", max_iterations=3)
        
        assert not state.is_max_iterations_reached()
        
        state.iteration_count = 3
        assert state.is_max_iterations_reached()
    
    def test_complete_with_answer(self):
        """Test completing agent with final answer."""
        state = AgentState(session_id="test", user_input="test")
        
        state.complete_with_answer("Final answer here")
        
        assert state.is_complete
        assert state.final_answer == "Final answer here"
        assert state.current_step == AgentStep.FINAL
        assert len(state.thoughts) == 1
        assert state.thoughts[0].step == AgentStep.FINAL


class TestToolCall:
    """Test cases for ToolCall model."""
    
    def test_tool_call_creation(self):
        """Test tool call creation."""
        tool_call = ToolCall(
            tool_name="calculator",
            arguments={"expression": "2+2"}
        )
        
        assert tool_call.tool_name == "calculator"
        assert tool_call.arguments == {"expression": "2+2"}
        assert tool_call.call_id is None
        assert isinstance(tool_call.timestamp, datetime)


class TestToolResult:
    """Test cases for ToolResult model."""
    
    def test_successful_tool_result(self):
        """Test successful tool result."""
        tool_call = ToolCall(tool_name="calculator", arguments={"expression": "2+2"})
        result = ToolResult(
            tool_call=tool_call,
            result={"answer": 4},
            success=True
        )
        
        assert result.tool_call == tool_call
        assert result.result == {"answer": 4}
        assert result.success
        assert result.error is None
    
    def test_failed_tool_result(self):
        """Test failed tool result."""
        tool_call = ToolCall(tool_name="calculator", arguments={"expression": "invalid"})
        result = ToolResult(
            tool_call=tool_call,
            result=None,
            success=False,
            error="Invalid expression"
        )
        
        assert result.tool_call == tool_call
        assert result.result is None
        assert not result.success
        assert result.error == "Invalid expression"


class TestAgentResponse:
    """Test cases for AgentResponse model."""
    
    def test_agent_response_creation(self):
        """Test agent response creation."""
        state = AgentState(session_id="test", user_input="test")
        state.complete_with_answer("Test answer")
        
        response = AgentResponse(
            state=state,
            answer="Test answer",
            reasoning_chain=state.thoughts,
            tools_used=["calculator"],
            total_iterations=1
        )
        
        assert response.answer == "Test answer"
        assert response.tools_used == ["calculator"]
        assert response.total_iterations == 1
        assert response.success
        assert response.error is None
    
    def test_tool_usage_summary(self):
        """Test tool usage summary."""
        state = AgentState(session_id="test", user_input="test")
        
        # Add some tool calls
        state.add_tool_call("calculator", {"expression": "2+2"})
        state.add_tool_call("web_search", {"query": "test"})
        state.add_tool_call("calculator", {"expression": "3+3"})
        
        response = AgentResponse(
            state=state,
            answer="Test",
            reasoning_chain=state.thoughts,
            tools_used=["calculator", "web_search"],
            total_iterations=1
        )
        
        usage_summary = response.get_tool_usage_summary()
        assert usage_summary["calculator"] == 2
        assert usage_summary["web_search"] == 1


class TestAgentConfig:
    """Test cases for AgentConfig model."""
    
    def test_default_config(self):
        """Test default configuration."""
        config = AgentConfig()
        
        assert config.model == "gpt-4"
        assert config.temperature == 0.1
        assert config.max_tokens is None
        assert config.max_iterations == 10
        assert not config.verbose
        assert config.system_message is None
    
    def test_custom_config(self):
        """Test custom configuration."""
        config = AgentConfig(
            model="gpt-3.5-turbo",
            temperature=0.5,
            max_iterations=5,
            verbose=True
        )
        
        assert config.model == "gpt-3.5-turbo"
        assert config.temperature == 0.5
        assert config.max_iterations == 5
        assert config.verbose