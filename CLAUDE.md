# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Testing
```bash
uv run pytest                    # Run all tests
uv run pytest tests/chat/        # Run chat component tests
uv run pytest tests/rag/         # Run RAG component tests
uv run pytest -v                 # Verbose test output
uv run pytest -k "test_name"     # Run specific test
```

### Code Quality
```bash
uv run black .                   # Format code with Black (line-length=88)
uv run isort .                   # Sort imports
uv run mypy src/                 # Type checking
pre-commit run --all-files       # Run pre-commit hooks
```

### Package Management
```bash
uv add <package>                 # Add new dependency
uv add --dev <package>           # Add development dependency
uv sync                          # Install dependencies from uv.lock
uv sync --extra dev              # Install with dev dependencies
uv run python script.py         # Run Python script with uv
```

### Build and Install
```bash
uv sync --extra dev              # Sync all dependencies including dev
uv pip install -e .             # Install in development mode
uv pip install -e ".[dev]"      # Install with dev dependencies
```

## Architecture Overview

This is a modular Python package for building AI product prototypes with three main components:

### Chat Component (`src/ai_building_blocks/chat/`)
- **Purpose**: Simple chat interfaces using LangChain and OpenAI
- **Key Files**:
  - `models.py`: Pydantic models for ChatMessage, ChatResponse, ChatHistory
  - `client.py`: ChatClient class with conversation history management
- **Features**: Message history, system messages, multiple message support
- **Usage**: Import `ChatClient` for basic conversational AI

### RAG Component (`src/ai_building_blocks/rag/`)
- **Purpose**: Retrieval-Augmented Generation with document indexing and vector search
- **Key Files**:
  - `models.py`: Document, RAGResponse, VectorStoreConfig, IndexStats models
  - `client.py`: RAGClient class for question-answering with context
  - `vectorstore.py`: Vector storage and similarity search functionality
- **Features**: Document ingestion, semantic search, contextual Q&A
- **Usage**: Import `RAGClient` for knowledge-base driven responses

### Agent Component (`src/ai_building_blocks/agent/`)
- **Purpose**: React (Reasoning and Acting) agent with tool usage and multi-step reasoning
- **Key Files**:
  - `models.py`: AgentState, ToolCall, ToolResult, AgentResponse data models
  - `tools.py`: Tool abstraction, ToolRegistry, and built-in tools (calculator, web search, file ops)
  - `client.py`: ReactAgent and AgentClient for intelligent task execution
- **Features**: Multi-step reasoning, tool execution, state management, conversation memory
- **Usage**: Import `AgentClient` for intelligent agents that can use tools

## Development Notes

### Dependencies
- Core: langchain, langchain-openai, langgraph, openai, pydantic
- Vector operations: numpy, tiktoken
- Environment: python-dotenv for API key management
- Testing: pytest, pytest-asyncio
- Code quality: black, isort, mypy, pre-commit
- Package manager: uv (for fast dependency management)

### Configuration
- Python 3.11+ required
- Uses pyproject.toml for project configuration
- uv for package management with uv.lock for dependency pinning
- Black formatting with 88 character line length
- MyPy type checking enabled with strict settings
- Environment variables loaded via python-dotenv

### Testing Strategy
- Tests organized by component in `tests/` directory
- Model validation tests for Pydantic schemas
- Example scripts in `examples/` demonstrate usage patterns

### API Keys
- Requires OPENAI_API_KEY environment variable
- Examples check for API key presence before running
- Uses .env files for local development (not in repository)